# Dify标签处理器使用说明

## 功能概述

这个重新编写的`process_labels_with_dify.py`脚本实现了以下功能：

1. **智能问题生成**：读取每组数据的现有问题数量，自动计算需要生成的问题数量（目标40个问题）
2. **Dify输出解析**：正确解析Dify的固定输出格式 `{"answer": ["问题内容"]}`
3. **并行处理**：支持多线程并发调用Dify API，大大提高处理效率
4. **断点续传**：支持中断后继续处理，避免重复工作
5. **进度显示**：使用tqdm显示处理进度
6. **完整格式保持**：输出文件与输入文件格式完全一致

## 主要改进

### 1. 智能问题数量计算
```python
current_count = len(existing_questions)  # 现有问题数量
needed_count = max(0, target_questions_count - current_count)  # 需要生成的数量
```

### 2. 正确的Dify输出解析
```python
def parse_dify_output(self, output: str) -> Optional[str]:
    """解析Dify输出，提取answer字段中的问题"""
    parsed_data = json.loads(output)
    if isinstance(parsed_data, dict) and "answer" in parsed_data:
        answer_list = parsed_data["answer"]
        if isinstance(answer_list, list) and len(answer_list) > 0:
            return answer_list[0]  # 返回第一个answer元素
```

### 3. 并行处理
```python
def generate_questions_parallel(self, label_path: str, existing_questions: List[str], needed_count: int):
    """并行生成指定数量的问题"""
    with ThreadPoolExecutor(max_workers=self.max_concurrent_requests) as executor:
        # 提交所有任务并并行执行
        future_to_index = {
            executor.submit(self.run_dify_workflow, label_path, existing_questions): i 
            for i in range(needed_count)
        }
```

## 配置参数

```python
processor = DifyLabelProcessor(
    dify_api_key="your_api_key",
    workflow_id="your_workflow_id",
    input_json_path=r"D:\python_project\dify\ultimate_merge_small_labels_numbered.json",
    output_json_path="processed_labels_output.json",
    dify_base_url="https://dify.xmdas-link.com",
    target_questions_count=40,      # 目标问题数量
    max_concurrent_requests=5       # 最大并发请求数
)
```

## 使用方法

### 1. 测试模式
```bash
python process_labels_with_dify.py --test 0
```
测试处理第1个数据项（索引从0开始）

### 2. 完整处理模式
```bash
python process_labels_with_dify.py
```
处理所有数据项

## 输入数据格式

```json
[
  {
    "label_path": "小学数学新知识树 -> 数与代数 -> ...",
    "example_questions": [
      "问题1",
      "问题2",
      "..."
    ]
  }
]
```

## 输出数据格式

输出格式与输入格式完全一致，只是`example_questions`数组中会包含更多问题（目标40个）。

## Dify输出格式要求

脚本期望Dify返回以下格式的JSON：

```json
{
  "answer": [
    "1、小明买了一些苹果，每箱苹果有20个。如果他一共买了5箱，请问他一共买了多少个苹果？首先计算5箱苹果总共有多少个，即$20 \\times 5 = 100$个，因此小明一共买了100个苹果。"
  ]
}
```

## 性能优化

1. **并发处理**：默认最多5个并发请求，可根据API限制调整
2. **连接池**：使用HTTP连接池复用连接
3. **重试机制**：自动重试失败的请求
4. **断点续传**：避免重复处理已完成的数据

## 日志和监控

- 详细的日志记录，包括成功/失败统计
- 实时进度显示
- 错误处理和异常捕获
- 处理结果自动保存

## 注意事项

1. 确保Dify API密钥和工作流ID正确
2. 根据API限制调整并发请求数量
3. 定期检查日志文件了解处理状态
4. 输出文件会在每处理完一个数据项后自动保存，支持断点续传

## 测试验证

运行测试脚本验证功能：
```bash
python test_dify_processor.py
```

测试包括：
- Dify输出解析功能
- 问题数量计算逻辑
- 数据结构验证
